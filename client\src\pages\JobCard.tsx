import { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import NewJobCardForm from "@/components/jobcard/NewJobCard";
import { JobCardPaperForm } from "@/components/jobcard/JobCardPaperForm";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Search,
  PlusCircle,
  Calendar,
  FileText,
  MoreVertical,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { formatDate, getStatusColor, formatStatusLabel } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { fetchAPI } from "@/lib/api";

const JobCard = () => {
  const [location, navigate] = useLocation();
  const isNewJob = location === "/job-card/new";
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: jobCards = [], isLoading } = useQuery<any[]>({
    queryKey: ["/api/job-card/list"],
    queryFn: async () => {
      try {
        const response = await fetchAPI("/job-card/list", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          }
        });
        if (!response.ok) {
          const errorData = await response.json();
          console.error("Error fetching gate pass:", errorData);
          throw new Error(errorData.message || "Failed to fetch gate pass");
        }
        return await response.json();
      } catch (error) {
        console.error("Error fetching gate pass:", error);
        toast({
          title: "Failed to load gate pass",
          description:
            "There was an error loading gate pass. This is just a prototype, so no actual data is fetched.",
          variant: "destructive",
        });
        return [];
      }
    },
    enabled: !isNewJob,
  });

  useEffect(() => {
    if (isNewJob) {
      queryClient.invalidateQueries({ queryKey: ["/api/gate-pass/list"] });
    }
  }, [isNewJob, queryClient]);


  // Mutation for generating invoice
  // const generateInvoiceMutation = useMutation({
  //   mutationFn: async (id: number) => {
  //     try {
  //       console.log("Generating invoice for job card ID:", id);
  //       const response = await apiRequest("POST", `/api/invoices`, {
  //         jobCardId: id,
  //       });

  //       if (!response.ok) {
  //         const errorData = await response.json();
  //         console.error("Invoice generation error:", errorData);
  //         throw new Error(errorData.message || "Failed to generate invoice");
  //       }

  //       return response.json();
  //     } catch (error) {
  //       console.error("Invoice generation error:", error);
  //       throw error;
  //     }
  //   },
  //   onSuccess: (data) => {
  //     console.log("Invoice generated successfully:", data);
  //     toast({
  //       title: "Invoice Generated",
  //       description: `Invoice #${
  //         data.invoiceNumber || "INV-001"
  //       } has been generated successfully.`,
  //     });
  //     queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });

  //     // Navigate to the invoice page
  //     if (data && data.id) {
  //       navigate(`/invoices/${data.id}`);
  //     } else {
  //       // If we don't have a real invoice ID, create a simulated one for the prototype
  //       const simulatedInvoiceId = Math.floor(Math.random() * 1000);
  //       navigate(`/invoices/${simulatedInvoiceId}`);
  //     }
  //   },
  //   onError: (error) => {
  //     console.error("Mutation error:", error);
  //     toast({
  //       title: "Failed to generate invoice",
  //       description:
  //         "There was an error generating the invoice. This is just a prototype, so no actual invoice is created.",
  //       variant: "destructive",
  //     });
  //   },
  // });

  const filteredJobCards = jobCards.filter((job: any) => {
    const matchesSearch =
      job.id.toString().includes(searchQuery) ||
      job.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === "ALL" || job.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // const filteredJobCards = [{
  //   id: 1,
  //   jobNumber: "JC-001",
  //   description: "Oil Change and Tire Rotation",
  //   status: "IN_PROGRESS",
  //   createdAt: new Date().toISOString(),
  //   estimatedCompletionDate: new Date(Date.now() + 86400000).toISOString(), // 1 day later
  // }]

  if (isNewJob) {
    return (
      <div className="py-6">
        <div className="max-w-10xl mx-auto px-4 sm:px-6 md:px-8">
          <h1 className="text-2xl font-semibold text-gray-900">
            Create New Job Card
          </h1>
          {/* <p className="mt-2 text-gray-600">
            Create a new job card for vehicle service
          </p> */}
          <div className="mt-6">
            {/* Paper-style job card form */}
            {/* <JobCardPaperForm /> */}
            <NewJobCardForm />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6">
      <div className="max-w-12xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Job Card</h1>
            <p className="mt-1 text-gray-600">
              Manage and view all service job cards
            </p>
          </div>
          <Button
            className="bg-primary hover:bg-primary/90"
            onClick={() => navigate("/job-card/new")}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Create New Job Card
          </Button>
        </div>

        <div className="mt-6">
          <Tabs defaultValue="active">
            {/* <TabsList className="w-full grid grid-cols-3 mb-6">
              <TabsTrigger value="active">Active Jobs</TabsTrigger>
              <TabsTrigger value="pending">Pending Approval</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList> */}

            <TabsContent value="active">
              <Card>
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <CardTitle>Active Service Jobs</CardTitle>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <div className="relative">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                        <Input
                          type="search"
                          placeholder="Search job card..."
                          className="pl-8 w-full sm:w-64"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                      <Select
                        defaultValue="ALL"
                        onValueChange={(value) => setStatusFilter(value)}
                      >
                        <SelectTrigger className="w-full sm:w-40">
                          <SelectValue placeholder="All Statuses" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">All Statuses</SelectItem>
                          <SelectItem value="IN_PROGRESS">
                            In Progress
                          </SelectItem>
                          <SelectItem value="PENDING">Pending</SelectItem>
                          <SelectItem value="PARTS_WAITING">
                            Parts Waiting
                          </SelectItem>
                          <SelectItem value="QUALITY_CHECK">
                            Quality Check
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="text-center py-4">Loading gate pass...</div>
                  ) : filteredJobCards.length > 0 ? (
                    <div className="overflow-x-auto -mx-4 sm:-mx-6">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              Id
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              Description
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              Status
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              Created
                            </th>
                            <th
                              scope="col"
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              Estimated Completion
                            </th>
                            <th scope="col" className="relative px-6 py-3">
                              <span className="sr-only">Actions</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredJobCards.map((job: any) => (
                            <tr key={job.id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">
                                  {job.id}
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm text-gray-900">
                                  {job.jobDescription}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span
                                  className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(
                                    job.status
                                  )}`}
                                >
                                  {formatStatusLabel(job.status)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {formatDate(job.createdAt)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex items-center">
                                  <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                                  {job.estimatedDeliveryDate
                                    ? formatDate(job.estimatedDeliveryDate)
                                    : "Not set"}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div className="flex items-center justify-end space-x-2">
                                  <Button
                                    variant="link"
                                    className="text-primary"
                                    onClick={() =>
                                      navigate(`/gate-pass/${job.id}`)
                                    }
                                  >
                                    View Details
                                  </Button>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        className="h-8 w-8 p-0"
                                      >
                                        <span className="sr-only">
                                          Open menu
                                        </span>
                                        <MoreVertical className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    {/* <DropdownMenuContent align="end">
                                      <DropdownMenuItem
                                        onClick={() =>
                                          generateInvoiceMutation.mutate(job.id)
                                        }
                                        disabled={
                                          generateInvoiceMutation.isPending
                                        }
                                        className="cursor-pointer"
                                      >
                                        <FileText className="mr-2 h-4 w-4" />
                                        Generate Invoice
                                      </DropdownMenuItem>
                                    </DropdownMenuContent> */}
                                  </DropdownMenu>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500">
                        No gate pass found matching your criteria
                      </p>
                      <Button className="mt-4" asChild>
                        <Link href="/job-cards/new">Create New Job</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="pending">
              <Card>
                <CardHeader>
                  <CardTitle>Jobs Pending Approval</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-4 text-gray-500">
                    Jobs that require customer, insurance, or warranty approval
                    will appear here.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="completed">
              <Card>
                <CardHeader>
                  <CardTitle>Completed Jobs</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-4 text-gray-500">
                    Jobs that have been completed and are ready for pickup or
                    have been delivered.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default JobCard;
