import { desc } from 'drizzle-orm';
import { pgTable, text, serial, timestamp, integer, json, boolean } from 'drizzle-orm/pg-core';

export const users = pgTable('users', {
  id: serial('id').primaryKey(), // <-- correct for PostgreSQL auto-increment
  username: text('username').notNull().unique(),
  password: text('password').notNull(), // hashed
  client: text('client').notNull(), // client identifier (e.g., garage name)
  createdAt: timestamp('created_at').defaultNow(),
});

// vehicle parts schema
export const vehicleParts = pgTable('vehicle_parts', {
  id: serial('id').primaryKey(),
  itemName: text('item_name').notNull(),
});

// services schema
export const services = pgTable('services', {
  id: serial('id').primaryKey(),
  itemName: text('service_name').notNull(),
});


// vehicle schema with columns vehicle registration number, make, model, color, engine number and chassis number
export const vehicles = pgTable('vehicles', {
  id: serial('id').primaryKey(),
  vehicleRegistrationNumber: text('vehicle_registration_number').notNull().unique(),
  make: text('make').notNull(),
  model_model: text('model_model').notNull(),
  color: text('color').notNull(),
  engineNumber: text('engine_number').notNull(),
  chassisNumber: text('chassis_number').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().$onUpdateFn(() => new Date()),
});



// gate pass schema with columns vehicle id, owner name, customer name, email,phone, service type, mileage, estimated delivery date, job description, remarks
export const gatePass = pgTable('gate_pass', {
  id: serial('id').primaryKey(),
  vehicleId: integer('vehicle_id').notNull().references(() => vehicles.id),
  ownerName: text('owner_name').notNull(),
  customerName: text('customer_name').notNull(),
  email: text('email').notNull(),
  phone: text('phone').notNull(),
  serviceType: text('service_type').notNull(),
  mileage: text('mileage').notNull(),
  estimatedDeliveryDate: timestamp('estimated_delivery_date').notNull(),
  jobDescription: text('job_description').notNull(),
  remarks: text('remarks'),
  status: text('status').notNull().default('pending'),
  canvas_json: json('canvas_json').default(null),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().$onUpdateFn(() => new Date()),
});



// schema for quotations with columns id,gate pass id ,parts,services ,discount, tax, total, valid until, notes, internal notes, terms and conditions, warranty, is insurance claim, insurance provider, claim number
// export const quotations = pgTable('quotations', {
//   id: serial('id').primaryKey(),
//   gatePassId: integer('gate_pass_id').notNull().references(() => gatePass.id),
//   parts: json('parts').notNull(),
//   services: json('services').notNull(),
//   discount: text('discount').notNull(),
//   tax: text('tax').notNull(),
//   total: text('total').notNull(),
//   validUntil: timestamp('valid_until').notNull(),
//   notes: text('notes'),
//   internalNotes: text('internal_notes'),
//   termsAndConditions: text('terms_and_conditions'),
//   warranty: text('warranty'),
//   isInsuranceClaim: boolean('is_insurance_claim').notNull().default(false),
//   insuranceProvider: text('insurance_provider'),
//   claimNumber: text('claim_number'),
//   createdAt: timestamp('created_at').defaultNow(),
//   updatedAt: timestamp('updated_at').defaultNow().$onUpdateFn(() => new Date()),
// });


// schema for quotation with columns id, gate pass id, parts_used, services_used, tax , notes, description , file_name , status ,id should start from 1000 also save the file in public folder with name as file_name 
export const quotations = pgTable('quotations', {
  id: serial('id').primaryKey(),
  gatePassId: integer('gate_pass_id').notNull().references(() => gatePass.id),
  partsUsed: json('parts_used').notNull(),
  servicesUsed: json('services_used').notNull(),
  tax: text('tax').notNull(),
  notes: text('notes'),
  description: text('description'),
  status: text('status').notNull().default('pending'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().$onUpdateFn(() => new Date()),
});


// schema for job card with complete details including vehicle and customer details
export const jobCards = pgTable('job_cards', {
  id: serial('id').primaryKey(),
  gatePassId: integer('gate_pass_id').notNull().references(() => gatePass.id),
  quotationId: integer('quotation_id').notNull().references(() => quotations.id),
  vehicleId: integer('vehicle_id').notNull().references(() => vehicles.id),
  customerId: integer('customer_id').notNull().references(() => users.id),
  description: text('description').notNull(),
  status: text('status').notNull().default('pending'),
  estimatedCompletionDate: timestamp('estimated_completion_date').notNull(),
  partsUsed: json('parts_used').notNull(),
  servicesUsed: json('services_used').notNull(),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().$onUpdateFn(() => new Date()),
});




