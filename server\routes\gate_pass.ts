// create api for gate pass
import express from 'express';
import { db } from '../db';
import { gatePass , vehicles , vehicleParts ,quotations ,services ,jobCards, users } from '../schema';
import { eq , desc ,sql  } from 'drizzle-orm';
import e from 'express';
import { i } from 'node_modules/vite/dist/node/types.d-aGj9QkWt';
import SibApiV3Sdk from 'sib-api-v3-sdk';
import pdf from 'html-pdf-node';
// import { v4 as uuidv4 } from 'uuid';
import path from "path";
import fs from "fs";

const router = express.Router();
// Check if the server is running
router.get("/", (_req, res) => {
  res.send("Garage ERP API is running 🚗");
});

// api for fetching vehicle details accept keyword and return vehicle details
// http://127.0.0.1:5000/api/gate-pass/vehicle/search?keyword=KL
router.get("/vehicle/search", async (req, res) => {
  const { keyword } = req.query;
  if (!keyword || typeof keyword !== "string") {
    return res.status(400).json({ error: "Keyword is required" });
  }
  try {
    const result = await db
      .select()
      .from(vehicles)
      .where(
        sql`${vehicles.vehicleRegistrationNumber} ILIKE ${`%${keyword}%`} OR
                    ${vehicles.make} ILIKE ${`%${keyword}%`} OR
                    ${vehicles.model_model} ILIKE ${`%${keyword}%`}`
      )
      .limit(10);

    res.status(200).json(result);
  } catch (error) {
    console.error("Error searching vehicles:", error);
    res.status(500).json({ error: "Failed to search vehicles" });
  }
});

// api for fetching latest customer data for a vehicle from the most recent gate pass
router.get("/vehicle/:vehicleId/latest-customer", async (req, res) => {
  const { vehicleId } = req.params;
  console.log(`Fetching latest customer data for vehicle ID: ${vehicleId}`);

  if (!vehicleId) {
    return res.status(400).json({ error: "Vehicle ID is required" });
  }

  try {
    const result = await db
      .select({
        customerName: gatePass.customerName,
        email: gatePass.email,
        phone: gatePass.phone,
        ownerName: gatePass.ownerName,
      })
      .from(gatePass)
      .where(eq(gatePass.vehicleId, parseInt(vehicleId)))
      .orderBy(desc(gatePass.createdAt))
      .limit(1);

    console.log(`Found ${result.length} customer records for vehicle ${vehicleId}`);

    if (result.length === 0) {
      console.log(`No customer data found for vehicle ${vehicleId}`);
      return res.status(404).json({ error: "No customer data found for this vehicle" });
    }

    console.log(`Returning customer data:`, result[0]);
    res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error fetching latest customer data:", error);
    res.status(500).json({ error: "Failed to fetch customer data" });
  }
});

// api for listing all vehicle parts accept keyword and return 10 records
router.get("/vehicle-parts/search", async (req, res) => {
  const { keyword } = req.query;
  if (!keyword || typeof keyword !== "string") {
    return res.status(400).json({ error: "Keyword is required" });
  }
  try {
    const result = await db
      .select()
      .from(vehicleParts)
      .where(sql`${vehicleParts.itemName} ILIKE ${`%${keyword}%`}`)
      .limit(10);

    res.status(200).json(result);
  } catch (error) {
    console.error("Error searching vehicle parts:", error);
    res.status(500).json({ error: "Failed to search vehicle parts" });
  }
});

// api for listing all services accept keyword and return 10 records
router.get("/services/search", async (req, res) => {
  const { keyword } = req.query;
  if (!keyword || typeof keyword !== "string") {
    return res.status(400).json({ error: "Keyword is required" });
  }
  try {
    const result = await db
      .select()
      .from(services)
      .where(sql`${services.itemName} ILIKE ${`%${keyword}%`}`)
      .limit(10);

    res.status(200).json(result);
  } catch (error) {
    console.error("Error searching services:", error);
    res.status(500).json({ error: "Failed to search services" });
  }
});

// create api for gate pass if vehicle is not in the system, create a new vehicle and then create a gate pass
router.post("/create", async (req, res) => {
  const {
    vehicleId: requestVehicleId,
    vehicleNumber,
    customerName,
    customerContact,
    owner_name,
    serviceType,
    make_model,
    customer_email,
    mileage,
    status,
    estimatedDeliveryDate,
    description,
    engine_number,
    chassis_number,
    remarks,
    canvasJson,
  } = req.body;

  // Log the received data for debugging
  console.log("Received data:", {
    customerName,
    vehicleNumber,
    owner_name,
  });

  // Validate required fields
  if (!vehicleNumber) {
    return res.status(400).json({ error: "Vehicle number is required" });
  }

  try {
    // Check if the vehicle already exists
    const existingVehicle = await db
      .select()
      .from(vehicles)
      .where(eq(vehicles.id, requestVehicleId))
      .limit(1);

    let vehicleId;

    if (existingVehicle.length === 0) {
      // Create a new vehicle
      const [make, model] = make_model.split(" ");
      const newVehicle = {
        vehicleRegistrationNumber: vehicleNumber,
        make: make || "Unknown",
        model_model: model || "Unknown", // Changed to match schema
        color: "Unknown",
        engineNumber: engine_number,
        chassisNumber: chassis_number,
      };

      const result = await db.insert(vehicles).values(newVehicle).returning();
      vehicleId = result[0].id;
    } else {
      vehicleId = existingVehicle[0].id;
    }

    // Create a new gate pass
    const newGatePass = {
      vehicleId,
      ownerName: owner_name || "Unknown", // Provide default
      customerName: customerName || "Unknown Customer", // Provide default
      email: customer_email || "", // Provide default
      phone: customerContact || "", // Provide default
      serviceType: serviceType || "PRIVATE", // Provide default
      mileage: mileage || "0",
      estimatedDeliveryDate: estimatedDeliveryDate ? new Date(estimatedDeliveryDate) : new Date(),
      jobDescription: description || "No description provided",
      remarks: remarks || "",
      status: status || "pending",
      canvas_json: canvasJson ? JSON.parse(canvasJson) : null,
    };

    console.log("Creating gate pass with data:", newGatePass);

    const gatePassResult = await db
      .insert(gatePass)
      .values(newGatePass)
      .returning();

    res.status(201).json({
      message: "Gate pass created successfully",
      gatePass: gatePassResult[0],
      isNewVehicle: existingVehicle.length === 0,
    });
  } catch (error) {
    console.error("Error creating gate pass:", error);
    res.status(500).json({ error: "Failed to create gate pass" });
  }
});

// api to list all gate passes id, description, status, created at, updated at, estimated completion date also accept keyword and search in gate pass id or vehicle registeration number

// api to list all gate passes id, description, status, created at, updated at, estimated completion date also accept keyword and search in gate pass id or vehicle registeration number
router.get("/list", async (req, res) => {
  const { keyword } = req.query;
  try {
    let query;

    if (keyword && typeof keyword === "string") {
      // Join gate pass with vehicles to search by vehicle registration number
      query = db
        .select({
          id: gatePass.id,
          jobDescription: gatePass.jobDescription,
          status: gatePass.status,
          createdAt: gatePass.createdAt,
          estimatedDeliveryDate: gatePass.estimatedDeliveryDate,
          make: vehicles.make,
          model_model: vehicles.model_model,
          vehicleId: gatePass.vehicleId,
          customerName: gatePass.customerName,
          vehicleRegistrationNumber: vehicles.vehicleRegistrationNumber,
        })
        .from(gatePass)
        .leftJoin(vehicles, eq(gatePass.vehicleId, vehicles.id))
        .where(
          sql`CAST(${gatePass.id} AS TEXT) ILIKE ${`%${keyword}%`} OR 
                    ${
                      vehicles.vehicleRegistrationNumber
                    } ILIKE ${`%${keyword}%`}`
        )
        .orderBy(desc(gatePass.createdAt));
    } else {
      // No keyword, return all records with vehicle registration
      query = db
        .select({
          id: gatePass.id,
          jobDescription: gatePass.jobDescription,
          make: vehicles.make,
          model_model: vehicles.model_model,
          status: gatePass.status,
          createdAt: gatePass.createdAt,
          estimatedDeliveryDate: gatePass.estimatedDeliveryDate,
          vehicleId: gatePass.vehicleId,
          customerName: gatePass.customerName,
          vehicleRegistrationNumber: vehicles.vehicleRegistrationNumber,
        })
        .from(gatePass)
        .leftJoin(vehicles, eq(gatePass.vehicleId, vehicles.id))
        .orderBy(desc(gatePass.createdAt));
    }

    const gatePasses = await query;
    res.status(200).json(gatePasses);
  } catch (error) {
    console.error("Error fetching gate passes:", error);
    res.status(500).json({ error: "Failed to fetch gate passes" });
  }
});

// api to get complete gate pass deatils including vehicle details by gate pass id
router.get("/details/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const gatePassResult = await db
      .select()
      .from(gatePass)
      .where(eq(gatePass.id, parseInt(id)))
      .limit(1);
    if (gatePassResult.length === 0) {
      return res.status(404).json({ error: "Gate pass not found" });
    }

    // Get the associated vehicle details
    const vehicleId = gatePassResult[0].vehicleId;
    const vehicleResult = await db
      .select()
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    // Combine gate pass and vehicle details
    const response = {
      ...gatePassResult[0],
      vehicle: vehicleResult.length > 0 ? vehicleResult[0] : null,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching gate pass details:", error);
    res.status(500).json({ error: "Failed to fetch gate pass details" });
  }
});

// Utility function to build invoice HTML
async function buildInvoiceHtml(generatedQuotationId: number, generatedGatePassId: number) {
  // Fetch data from DB
  const [jobCard] = await db
    .select()
    .from(gatePass)
    .where(eq(gatePass.id, generatedGatePassId))
    .limit(1);
  console.log('Job Card data:', jobCard);
  if (!jobCard) throw new Error('Gate pass not found.');

  const [vehicle] = await db
    .select()
    .from(vehicles)
    .where(eq(vehicles.id, jobCard.vehicleId))
    .limit(1);
  console.log('Vehicle data:', vehicle);
  const [quotation] = await db
    .select()
    .from(quotations)
    .where(eq(quotations.id, generatedQuotationId))
    .limit(1);

  if (!quotation) throw new Error('Quotation not found.');
  console.log('Quotation data:', quotation);
  // Parse parts and services
  const submittedParts = typeof quotation.partsUsed === 'string' ? JSON.parse(quotation.partsUsed) : (quotation.partsUsed || []);
  const submittedServices = typeof quotation.servicesUsed === 'string' ? JSON.parse(quotation.servicesUsed) : (quotation.servicesUsed || []);

  // Calculate total
  const calculateTotalCost = () => {
    let total = 0;
    [...submittedParts, ...submittedServices].forEach((item: any) => {
      total += parseFloat(item.price || 0);
    });
    return total;
  };

  // Build HTML
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Job Card Invoice</title>
  <style>
    * { box-sizing: border-box; }
    html, body { height: 100%; margin: 0; font-family: Arial, sans-serif; font-size: 13px; }
    body { display: flex; flex-direction: column; padding: 30px; }
    .content { flex: 1; }
    .header { text-align: center; font-weight: bold; font-size: 16px; }
    .sub-header { text-align: center; font-size: 12px; margin-bottom: 20px; }
    table { width: 100%; border-collapse: collapse; border: 1px solid black }
    .details-table td { border: 1px solid #000; padding: 6px; vertical-align: top; }
    .billing-table th { border: 1px solid #000; padding: 10px; vertical-align: top; height: 40px; background-color: #f2f2f2; text-align: left; }
    .billing-table td { border-bottom: None; border-right: 1px solid black; padding: 10px; vertical-align: top; height: 40px; }
    .summary-table td { border-bottom: None; padding: 10px; height: 30px; }
    .summary-table .label { text-align: right; width: 85%; }
    .summary-table .value { text-align: right; width: 15%; border: 1px solid #000; }
    .grand-total-row { font-weight: bold; }
    .declaration { border-top: 1px solid #000; margin-top: 30px; padding-top: 10px; }
    .signature { margin-top: 60px; text-align: right; }
  </style>
</head>
<body>
  <div class="content">
    <div class="header">
      M R S AUTO MAINTENANCE L.L.C
    </div>
    <div class="sub-header">
      Tel: +971 55 994 1284, +971 55 994 1285 | Email: <EMAIL>
    </div>
    <h2 style="text-align: center;">QUOTATION</h2>
    <table class="details-table">
      <tr>
        <td><strong>Quotation ID: </strong> ${jobCard?.id || ""}</td>
        <td><strong>Date:</strong> ${
          jobCard?.createdAt
            ? new Date(jobCard.createdAt).toLocaleDateString()
            : ""
        }</td>
        <td><strong>Time:</strong> ${
          jobCard?.createdAt
            ? new Date(jobCard.createdAt).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })
            : ""
        }</td>
      </tr>
      <tr>
        <td><strong>Driver Name:</strong> ${jobCard?.customerName || ""}</td>
        <td><strong>Make & Model:</strong> ${vehicle?.make || ""} ${vehicle?.model_model || ""}</td>
        <td><strong>Assigned to:</strong> ${"Unassigned"}</td>
      </tr>
      <tr>
        <td><strong>Chassis No:</strong> ${vehicle?.chassisNumber || ""}</td>
        <td><strong>Engine No:</strong> ${vehicle?.engineNumber || ""}</td>
        <td><strong>Reg No:</strong> ${vehicle?.vehicleRegistrationNumber || ""}</td>
      </tr>
      <tr>
        <td><strong>Delivery Date:</strong> ${
          jobCard?.estimatedDeliveryDate
            ? new Date(jobCard.estimatedDeliveryDate).toLocaleDateString()
            : ""
        }</td>
        <td><strong>Color:</strong> ${vehicle?.color || ""}</td>
        <td><strong>ODO Meter:</strong> ${jobCard?.mileage || ""}</td>
      </tr>
      <tr>
        <td><strong>Mobile No:</strong> ${jobCard?.phone || ""}</td>
        <td colspan="2"><strong>Veh. Reg. Card:</strong> ${vehicle?.registrationCard || "Unknown"}</td>
      </tr>
    </table>
    <br>
    <table class="billing-table">
      <thead>
        <tr>
          <th style="width: 5%;">S.N</th>
          <th style="width: 55%;">Description</th>
          <th style="width: 10%;">Qty</th>
          <th style="width: 10%;">Unit</th>
          <th style="width: 10%;">Price</th>
          <th style="width: 10%;">Amount (AED)</th>
        </tr>
      </thead>
      <tbody>
        ${[...(submittedParts || []), ...(submittedServices || [])]
          .map(
            (item: any, idx: number) => `
            <tr>
              <td>${idx + 1}</td>
              <td>${item.itemName || ""}</td>
              <td>1.00</td>
              <td>Pcs</td>
              <td>${parseFloat(item.price).toFixed(2)}</td>
              <td>${parseFloat(item.price).toFixed(2)}</td>
            </tr>
          `
          )
          .join("")}
        ${(() => {
          const totalRows =
            (submittedParts?.length || 0) + (submittedServices?.length || 0);
          let emptyRows = "";
          for (let i = totalRows; i < 4; i++) {
            emptyRows += `<tr><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>`;
          }
          return emptyRows;
        })()}
      </tbody>
    </table>
    <br>
    <table class="summary-table">
      <tr>
        <td class="label">Total</td>
        <td class="value">${calculateTotalCost().toFixed(2)}</td>
      </tr>
      <tr>
        <td class="label">Less : Excess</td>
        <td class="value">0.00</td>
      </tr>
      <tr>
        <td class="label">Less : Discount</td>
        <td class="value">0.00</td>
      </tr>
      <tr>
        <td class="label">Add : VAT @ 5.00%</td>
        <td class="value">${(calculateTotalCost() * 0.05).toFixed(2)}</td>
      </tr>
      <tr class="grand-total-row">
        <td class="label">Grand Total</td>
        <td class="value">${(calculateTotalCost() * 1.05).toFixed(2)}</td>
      </tr>
    </table>
    <br><strong>Dirhams ${
      Math.round(calculateTotalCost() * 1.05) || "Zero"
    } Only</strong>
  </div>
  <div class="declaration">
    <strong>DECLARATION</strong><br>
    I hereby authorize your garage to repair my vehicle.<br><br>
    Name: ______________________ <br>
    Signature: __________________
  </div>
  <div class="signature">
    for <strong>M R S Auto Maintenance LLC</strong><br>
    Authorised Signatory
  </div>
</body>
</html>
  `;
}

// api to send email with invoice details for a gate pass
router.post('/send-invoice-email', async (req, res) => {
  const { receiverEmail, generatedQuotationId, generatedGatePassId } = req.body;
  console.log('Received request to send invoice email:', { receiverEmail, generatedQuotationId, generatedGatePassId });

  try {
    // Build HTML using the decoupled function
    const html = await buildInvoiceHtml(generatedQuotationId, generatedGatePassId);

    // 1. Convert HTML to PDF Buffer
    const file = { content: html };
    const pdfBuffer = await pdf.generatePdf(file, { format: 'A4' });

    // 2. Encode PDF as Base64 for Brevo attachment
    const base64PDF = pdfBuffer.toString('base64');

    // 3. Configure Brevo client
    let defaultClient = SibApiV3Sdk.ApiClient.instance;
    let apiKey = defaultClient.authentications['api-key'];
    apiKey.apiKey = "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"; // Use your real API key (preferably from .env)

    const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();

    // Fetch jobCard for subject/filename
    const [jobCard] = await db
      .select()
      .from(gatePass)
      .where(eq(gatePass.id, generatedGatePassId))
      .limit(1);

    const sendSmtpEmail = {
      to: [{ email: receiverEmail }],
      sender: { email: '<EMAIL>', name: 'M R S Auto Maintenance' },
      subject: `Invoice for Job Card #${jobCard?.id || generatedGatePassId}`,
      htmlContent: `<p>Dear Customer,</p><p>Please find the invoice attached.</p>`,
      attachment: [
        {
          content: base64PDF,
          name: `Invoice_${jobCard?.id || generatedGatePassId}.pdf`,
        },
      ],
    };

    await apiInstance.sendTransacEmail(sendSmtpEmail);
    res.status(200).json({ message: 'Invoice email with PDF attachment sent successfully.' });

  } catch (error) {
    console.error('Error sending invoice email:', error);
    res.status(500).json({ error: 'Failed to send invoice email.' });
  }
});

// api to save quotation and file
router.post("/quotation/create", async (req, res) => {
  const {
    gatePassId,
    partsUsed,
    servicesUsed,
    jobDescription,
    remarks,
    customerName,
    vehicleNumber,
    totalAmount,
    tax,
  } = req.body;

  try {

    const newQuotation = {
      gatePassId,
      partsUsed: JSON.stringify(partsUsed),
      servicesUsed: JSON.stringify(servicesUsed),
      tax,
      notes: remarks,
      description: jobDescription,
      status: "pending",
    };

    const result = await db.insert(quotations).values(newQuotation).returning();


    res.status(201).json({
      ...result[0]
    });
  } catch (error) {
    console.error("Error creating quotation:", error);
    res.status(500).json({ error: "Failed to create quotation" });
  }
});

// api to save new quotation directly from new quotation page
router.post("/quotation/create_new", async (req, res) => {
  const {
    partsUsed,
    servicesUsed,
    jobDescription,
    remarks,
    customerName,
    customerEmail,
    customerPhone,
    customerAddress,
    vehicleMake,
    vehicleModel,
    vehicleRegistrationNumber,
    vehicleColor,
    vehicleEngineNumber,
    vehicleChassisNumber,
    totalAmount,
    tax,
  } = req.body;

  try {
    // Create or find the vehicle first
    let vehicle;
    const existingVehicle = await db
      .select()
      .from(vehicles)
      .where(eq(vehicles.vehicleRegistrationNumber, vehicleRegistrationNumber))
      .limit(1);

    if (existingVehicle.length > 0) {
      vehicle = existingVehicle[0];
      // Update vehicle info if provided
      await db
        .update(vehicles)
        .set({
          make: vehicleMake,
          model_model: vehicleModel,
          color: vehicleColor,
          engineNumber: vehicleEngineNumber,
          chassisNumber: vehicleChassisNumber,
        })
        .where(eq(vehicles.id, vehicle.id));
    } else {
      // Create new vehicle
      const newVehicleResult = await db
        .insert(vehicles)
        .values({
          make: vehicleMake,
          model_model: vehicleModel,
          vehicleRegistrationNumber: vehicleRegistrationNumber,
          color: vehicleColor,
          engineNumber: vehicleEngineNumber,
          chassisNumber: vehicleChassisNumber,
        })
        .returning();
      vehicle = newVehicleResult[0];
    }

    // Create a minimal gate pass for the new quotation
    const newGatePass = {
      vehicleId: vehicle.id,
      ownerName: customerName,
      customerName: customerName,
      email: customerEmail,
      phone: customerPhone,
      serviceType: "Quotation Request",
      mileage: "0", // Default value for new quotations
      estimatedDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      jobDescription: jobDescription || "New quotation request",
      remarks: remarks || "",
      status: "quotation_pending",
      createdAt: new Date(),
    };

    const gatePassResult = await db.insert(gatePass).values(newGatePass).returning();
    const createdGatePass = gatePassResult[0];

    // Create the quotation
    const newQuotation = {
      gatePassId: createdGatePass.id,
      partsUsed: JSON.stringify(partsUsed || []),
      servicesUsed: JSON.stringify(servicesUsed || []),
      tax: tax?.toString() || "5.0",
      notes: remarks || "",
      description: jobDescription || "",
      status: "pending",
    };

    const result = await db.insert(quotations).values(newQuotation).returning();

    // Return the quotation with customer and vehicle details
    const quotationWithDetails = {
      ...result[0],
      customer: {
        name: customerName,
        email: customerEmail,
        phone: customerPhone,
        address: customerAddress,
      },
      vehicle: {
        id: vehicle.id,
        make: vehicle.make,
        model: vehicle.model_model,
        registrationNumber: vehicle.vehicleRegistrationNumber,
        color: vehicle.color,
        engineNumber: vehicle.engineNumber,
        chassisNumber: vehicle.chassisNumber,
      },
    };

    res.status(201).json(quotationWithDetails);
  } catch (error) {
    console.error("Error creating quotation:", error);
    res.status(500).json({
      error: "Failed to create quotation",
      details: error instanceof Error ? error.message : "Unknown error"
    });
  }
});


// api to list all quotations accept keyword and search in quatation id or vehicle registeration number
router.get("/quotation/list", async (req, res) => {
  const { keyword } = req.query;
  try {
    let query;

    if (keyword && typeof keyword === "string") {
      // Join gate pass with vehicles to search by vehicle registration number
      query = db
        .select({
          id: quotations.id,
          jobDescription: quotations.description,
          status: quotations.status,
          createdAt: quotations.createdAt,
          gatePassId: quotations.gatePassId,
          customerName: gatePass.customerName,
          vehicleRegistrationNumber: vehicles.vehicleRegistrationNumber,
        })
        .from(quotations)
        .leftJoin(gatePass, eq(quotations.gatePassId, gatePass.id))
        .leftJoin(vehicles, eq(gatePass.vehicleId, vehicles.id))
        .where(
          sql`CAST(${quotations.id} AS TEXT) ILIKE ${`%${keyword}%`} OR 
                    ${
                      vehicles.vehicleRegistrationNumber
                    } ILIKE ${`%${keyword}%`}`
        )
        .orderBy(desc(quotations.createdAt));
    } else {
      // No keyword, return all records with vehicle registration
      query = db
        .select({
          id: quotations.id,
          jobDescription: quotations.description,
          status: quotations.status,
          createdAt: quotations.createdAt,
          gatePassId: quotations.gatePassId,
          customerName: gatePass.customerName,
          vehicleRegistrationNumber: vehicles.vehicleRegistrationNumber,
        })
        .from(quotations)
        .leftJoin(gatePass, eq(quotations.gatePassId, gatePass.id))
        .leftJoin(vehicles, eq(gatePass.vehicleId, vehicles.id))
        .orderBy(desc(quotations.createdAt));
    }

    const quotationResults = await query;
    res.status(200).json(quotationResults);
  }
  catch (error) {
    console.error("Error fetching quotations:", error);
    res.status(500).json({ error: "Failed to fetch quotations" });
  }
});



// api to get complete quotation details with gate pass and vehicle information
router.get("/quotation/details/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const quotationResult = await db
      .select()
      .from(quotations)
      .where(eq(quotations.id, parseInt(id)))
      .limit(1);
    
    if (quotationResult.length === 0) {
      return res.status(404).json({ error: "Quotation not found" });
    }

    // Get the associated gate pass details
    const gatePassId = quotationResult[0].gatePassId;
    const gatePassResult = await db
      .select()
      .from(gatePass)
      .where(eq(gatePass.id, gatePassId))
      .limit(1);
    
    if (gatePassResult.length === 0) {
      return res.status(404).json({ error: "Associated gate pass not found" });
    }

    // Get the associated vehicle details
    const vehicleId = gatePassResult[0].vehicleId;
    const vehicleResult = await db
      .select()
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    // Parse JSON strings back to objects with error handling
    const parsedQuotation = {
      ...quotationResult[0],
      partsUsed: parseJsonSafely(quotationResult[0].partsUsed),
      servicesUsed: parseJsonSafely(quotationResult[0].servicesUsed)
    };

    // Combine quotation, gate pass, and vehicle details
    const response = {
      ...parsedQuotation,
      gatePass: gatePassResult[0],
      vehicle: vehicleResult.length > 0 ? vehicleResult[0] : null,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching quotation details:", error);
    res.status(500).json({ error: "Failed to fetch quotation details" });
  }
});

// Helper function to safely parse JSON
function parseJsonSafely(jsonString: any) {
  if (!jsonString) return [];
  
  // If it's already an object, return it directly
  if (typeof jsonString === 'object') return jsonString;
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return [];
  }
}



// api to update quotation
router.post("/quotation/update", async (req, res) => {
  const {
    quotationId,
    partsUsed,
    servicesUsed,
    jobDescription,
    remarks,
  } = req.body;

  try {
    const updateData: Partial<typeof req.body> = {};

    if (partsUsed) {
      updateData.partsUsed = JSON.stringify(partsUsed);
    }
    if (servicesUsed) { 
      updateData.servicesUsed = JSON.stringify(servicesUsed);
    }
    if (jobDescription) {
      updateData.description = jobDescription;
    }
    if (remarks) {
      updateData.notes = remarks;
    }

    const result = await db
      .update(quotations)
      .set(updateData)
      .where(eq(quotations.id, parseInt(quotationId)))
      .returning();

    res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error updating quotation:", error);
    res.status(500).json({ error: "Failed to update quotation" });
  }
});

// api to approve quotation
router.post("/quotation/approve/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(quotations)
      .set({ status: "approved" })
      .where(eq(quotations.id, parseInt(id)))
      .returning();

    res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error approving quotation:", error);
    res.status(500).json({ error: "Failed to approve quotation" });
  }
});



// api to create job card from quotation id  - take complete details from quotation and create a job card doont reference quotation id or job card id or gate pass id ,maintain complete data in schema
router.post("/job-card/create-from-quotation", async (req, res) => {
  const { quotationId } = req.body;

  if (!quotationId) {
    return res.status(400).json({ error: "Quotation ID is required" });
  }

  try {
    // Fetch quotation details
    const quotationResult = await db
      .select()
      .from(quotations)
      .where(eq(quotations.id, parseInt(quotationId)))
      .limit(1);

    if (quotationResult.length === 0) {
      return res.status(404).json({ error: "Quotation not found" });
    }

    const quotation = quotationResult[0];

    // Fetch associated gate pass details
    const gatePassResult = await db
      .select()
      .from(gatePass)
      .where(eq(gatePass.id, quotation.gatePassId))
      .limit(1);

    if (gatePassResult.length === 0) {
      return res.status(404).json({ error: "Associated gate pass not found" });
    }

    const gatePassData = gatePassResult[0];

    // Fetch associated vehicle details
    const vehicleResult = await db
      .select()
      .from(vehicles)
      .where(eq(vehicles.id, gatePassData.vehicleId))
      .limit(1);

    if (vehicleResult.length === 0) {
      return res.status(404).json({ error: "Associated vehicle not found" });
    }

    const vehicle = vehicleResult[0];

    // Generate unique job number
    const currentYear = new Date().getFullYear();
    const jobNumber = `JOB-${currentYear}-${Date.now()}`;

    // Calculate total amount from parts and services
    const partsUsed = parseJsonSafely(quotation.partsUsed);
    const servicesUsed = parseJsonSafely(quotation.servicesUsed);

    let totalAmount = 0;
    [...partsUsed, ...servicesUsed].forEach((item: any) => {
      totalAmount += parseFloat(item.price || 0);
    });

    // Add tax to total
    const taxRate = parseFloat(quotation.tax || "5") / 100;
    const totalWithTax = totalAmount * (1 + taxRate);

    // Create job card with complete embedded data
    const newJobCard = {
      jobNumber,

      // Complete vehicle details embedded
      vehicleRegistrationNumber: vehicle.vehicleRegistrationNumber,
      vehicleMake: vehicle.make,
      vehicleModel: vehicle.model_model,
      vehicleColor: vehicle.color,
      vehicleEngineNumber: vehicle.engineNumber,
      vehicleChassisNumber: vehicle.chassisNumber,

      // Complete customer details embedded
      customerName: gatePassData.customerName,
      ownerName: gatePassData.ownerName,
      customerEmail: gatePassData.email,
      customerPhone: gatePassData.phone,

      // Service details
      serviceType: gatePassData.serviceType,
      mileage: gatePassData.mileage,
      description: gatePassData.jobDescription,
      jobDescription: gatePassData.jobDescription,

      // Parts and services used (from quotation)
      partsUsed: JSON.stringify(partsUsed),
      servicesUsed: JSON.stringify(servicesUsed),

      // Financial details
      tax: quotation.tax,
      totalAmount: totalWithTax.toFixed(2),

      // Status and dates
      status: "pending",
      estimatedDeliveryDate: gatePassData.estimatedDeliveryDate,
      estimatedCompletionDate: gatePassData.estimatedDeliveryDate,

      // Additional details
      notes: quotation.notes,
      remarks: gatePassData.remarks,
      quotationNotes: quotation.notes,
      quotationDescription: quotation.description,
    };

    const result = await db.insert(jobCards).values(newJobCard).returning();

    res.status(201).json({
      message: "Job card created successfully from quotation",
      jobCard: result[0],
      sourceData: {
        quotationId: quotation.id,
        gatePassId: gatePassData.id,
        vehicleId: vehicle.id
      }
    });

  } catch (error) {
    console.error("Error creating job card from quotation:", error);
    res.status(500).json({
      error: "Failed to create job card from quotation",
      details: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

//api to list all job cards accept keyword and search in job card id or vehicle registeration number
router.get("/job-card/list", async (req, res) => {
  const { keyword } = req.query;
  try {
    let query;
    if (keyword && typeof keyword === "string") {
      // Join job cards with vehicles to search by vehicle registration number
      query = db
        .select({
          id: jobCards.id,
          jobNumber: jobCards.jobNumber,
          status: jobCards.status,
          createdAt: jobCards.createdAt,
          estimatedDeliveryDate: jobCards.estimatedDeliveryDate,
          make: vehicles.make,
          model_model: vehicles.model_model,
          vehicleId: jobCards.vehicleId,
          customerName: jobCards.customerName,
          vehicleRegistrationNumber: vehicles.vehicleRegistrationNumber,
        })
        .from(jobCards)
        .leftJoin(vehicles, eq(jobCards.vehicleId, vehicles.id))
        .where(
          sql`CAST(${jobCards.id} AS TEXT) ILIKE ${`%${keyword}%`} OR 
                    ${
                      vehicles.vehicleRegistrationNumber
                    } ILIKE ${`%${keyword}%`}`
        )
        .orderBy(desc(jobCards.createdAt));
    } else {
      // No keyword, return all records with vehicle registration
      query = db
        .select({
          id: jobCards.id,
          jobNumber: jobCards.jobNumber,
          status: jobCards.status,
          createdAt: jobCards.createdAt,
          estimatedDeliveryDate: jobCards.estimatedDeliveryDate,
          make: vehicles.make,
          model_model: vehicles.model_model,
          vehicleId: jobCards.vehicleId,






export default router;
