import { useState, useRef } from "react";
import { useLocation } from "wouter";
import {
  Card,
  CardContent,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
  } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  CalendarDays,
  Wrench,
  Car,
  User,
  ClipboardList,
  ChevronLeft,
  FileText,
  PlusCircle,
  Package,
  Printer,
  Mail,
  FileDown,
  } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { apiRequest } from "@/lib/queryClient";

const CreateJobCard = () => {
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Form state
  const [showAddPartDialog, setShowAddPartDialog] = useState(false);
  const [showAddServiceDialog, setShowAddServiceDialog] = useState(false);
  const [selectedPartId, setSelectedPartId] = useState<string>("");
  const [selectedServiceId, setSelectedServiceId] = useState<string>("");
  const [partPrize, setPartPrize] = useState("0.00");
  const [servicePrize, setServicePrize] = useState("1");
  const [partNotes, setPartNotes] = useState("");
  const [serviceNotes, setServiceNotes] = useState("");
  const [showInvoicePreview, setShowInvoicePreview] = useState(false);
  const [emailContact, setEmailContact] = useState("");
  const [description, setJobDescription] = useState("");
  const [remarks, setRemarks] = useState("");
  const [customerName, setCustomerName] = useState("");
  const [ownerName, setOwnerName] = useState("");
  const [customerEmail, setCustomerEmail] = useState("");
  const [customerPhone, setCustomerPhone] = useState("");
  const [customerAddress, setCustomerAddress] = useState("");
  const [vehicleMake, setVehicleMake] = useState("");
  const [vehicleModel, setVehicleModel] = useState("");
  const [vehicleReg, setVehicleReg] = useState("");
  const [vehicleColor, setVehicleColor] = useState("");
  const [vehicleEngine, setVehicleEngine] = useState("");
  const [vehicleChassis, setVehicleChassis] = useState("");
  const [partSearch, setPartSearch] = useState("");
  const [serviceSearch, setServiceSearch] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [filteredServices, setFilteredServices] = useState<any[]>([]);
  const [addedParts, setAddedParts] = useState<any[]>([]);
  const [submittedParts, setSubmittedParts] = useState<any[]>([]);
  const [selectedPartObj, setSelectedPartObj] = useState<any>(null);
  const [addedServices, setAddedServices] = useState<any[]>([]);
  const [submittedServices, setSubmittedServices] = useState<any[]>([]);
  const [selectedServiceObj, setSelectedServiceObj] = useState<any>(null);

  // No API calls, so search is just a dummy filter
  const handlePartSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPartSearch(e.target.value);
    // Optionally, implement local search/filter here
    setSearchResults([]);
  };

  const handleServiceSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setServiceSearch(e.target.value);
    setFilteredServices([]);
  };

  const handleAddSinglePart = () => {
    if (!selectedPartObj && !partSearch) return;
    setAddedParts((prev) => [
      ...prev,
      {
        id: selectedPartObj?.id || Date.now(),
        itemName: selectedPartObj?.itemName || partSearch,
        price: partPrize,
      },
    ]);
    setSelectedPartId("");
    setPartSearch("");
    setPartPrize("");
    setPartNotes("");
    setSelectedPartObj(null);
    setSearchResults([]);
  };

  const handleRemovePart = (index: number) => {
    setAddedParts((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmitAllParts = () => {
    setSubmittedParts((prev) => [...prev, ...addedParts]);
    setAddedParts([]);
    setShowAddPartDialog(false);
  };

  const handleAddSingleService = () => {
    if (!selectedServiceObj && !serviceSearch) return;
    setAddedServices((prev) => [
      ...prev,
      {
        id: selectedServiceObj?.id || Date.now(),
        itemName: selectedServiceObj?.itemName || serviceSearch,
        price: servicePrize,
      },
    ]);
    setSelectedServiceId("");
    setServiceSearch("");
    setServicePrize("");
    setServiceNotes("");
    setSelectedServiceObj(null);
    setFilteredServices([]);
  };

  const handleRemoveService = (index: number) => {
    setAddedServices((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmitAllServices = () => {
    setSubmittedServices((prev) => [...prev, ...addedServices]);
    setAddedServices([]);
    setShowAddServiceDialog(false);
  };

  const calculateTotalCost = () => {
    const serviceCost: number = submittedServices
      ? submittedServices.reduce(
          (total: number, service: any) => total + parseFloat(service.price),
          0
        )
      : 0;
    const partsCost: number = submittedParts
      ? submittedParts.reduce(
          (total: number, part: any) => total + parseFloat(part.price),
          0
        )
      : 0;
    return serviceCost + partsCost;
  };

  // Dummy invoice HTML for preview/print
  const buildInvoiceHtml = () => `
    <html>
    <head><title>Job Card Invoice</title></head>
    <body>
      <h2>Quotation</h2>
      <div>Customer: ${customerName}</div>
      <div>Vehicle: ${vehicleMake} ${vehicleModel}</div>
      <div>Description: ${description}</div>
      <div>Remarks: ${remarks}</div>
      <h3>Parts</h3>
      <ul>
        ${submittedParts.map((p) => `<li>${p.itemName}: AED ${p.price}</li>`).join("")}
      </ul>
      <h3>Services</h3>
      <ul>
        ${submittedServices.map((s) => `<li>${s.itemName}: AED ${s.price}</li>`).join("")}
      </ul>
      <h3>Total: AED ${calculateTotalCost().toFixed(2)}</h3>
    </body>
    </html>
  `;

  const printJobCard = () => {
    const html = buildInvoiceHtml();
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast({
        title: "Print Error",
        description: "Unable to open print window.",
        variant: "destructive",
      });
      return;
    }
    printWindow.document.write(html);
    setTimeout(() => {
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.addEventListener("afterprint", () => {
        printWindow.close();
      });
    }, 500);
  };

  // Dummy send email
  const sendInvoiceEmail = async (receiverEmail: string) => {
    toast({
      title: "Email Sent",
      description: `Quotation sent to ${receiverEmail}`,
    });
  };

  const handleCreateJobCard = async () => {
    // Prepare the job card data
    const jobCardData = {
      customer: {
        name: customerName,
        email: customerEmail,
        phone: customerPhone,
        address: customerAddress,
      },
      vehicle: {
        make: vehicleMake,
        model: vehicleModel,
        reg: vehicleReg,
        color: vehicleColor,
        engine: vehicleEngine,
        chassis: vehicleChassis,
      },
      description,
      remarks,
      parts: submittedParts,
      services: submittedServices,
      total: calculateTotalCost(),
    };

    try {
      // Replace '/api/job-cards' with your actual backend endpoint
      await fetch("/api/job-cards", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(jobCardData),
      });
      toast({
        title: "Job Card Created",
        description: "The job card has been successfully created.",
      });
      // Optionally, navigate or reset form here
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create job card.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="py-6 print-container">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Button
              variant="outline"
              onClick={() => navigate("/job-card")}
              className="mr-4 print-hidden"
            >
              <ChevronLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Create New Job Card
              </h1>
            </div>
          </div>
          <div className="flex items-center gap-4 print-hidden">
            <Button
              onClick={() => setShowInvoicePreview(true)}
              className="flex items-center gap-1"
            >
              <FileText className="h-4 w-4" />
              Preview Job Card
            </Button>
            <Button
              className="ml-2"
              onClick={handleCreateJobCard}
              disabled={
                !customerName ||
                !vehicleMake ||
                (!submittedParts.length && !submittedServices.length)
              }
            >
              Create Job Card
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <User className="mr-2 h-5 w-5 text-primary" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                
                <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="customer-name">Customer Name</Label>
                  <Input
                    id="customer-name"
                    className="font-medium"
                    value={customerName}
                    onChange={e => setCustomerName(e.target.value)}
                    placeholder="Customer Name"
                  />
                  </div>
                  <div className="flex-1">
                  <Label htmlFor="owner-name">Owner Name</Label>
                  <Input
                    id="owner-name"
                    className="font-medium"
                    value={ownerName}
                    onChange={e => setOwnerName(e.target.value)}
                    placeholder="Owner Name"
                  />
                </div>
                </div>
                <div>
                  <Label htmlFor="customer-email">Email</Label>
                  <Input
                    id="customer-email"
                    className="text-sm"
                    value={customerEmail}
                    onChange={e => setCustomerEmail(e.target.value)}
                    placeholder="Email"
                  />
                </div>
                <div>
                  <Label htmlFor="customer-phone">Phone</Label>
                  <Input
                    id="customer-phone"
                    className="text-sm"
                    value={customerPhone}
                    onChange={e => setCustomerPhone(e.target.value)}
                    placeholder="Phone"
                  />
                </div>
                <div>
                  <Label htmlFor="customer-address">Address</Label>
                  <Input
                    id="customer-address"
                    className="text-sm"
                    value={customerAddress}
                    onChange={e => setCustomerAddress(e.target.value)}
                    placeholder="Address"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center text-lg">
                <Car className="mr-2 h-5 w-5 text-primary" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Label htmlFor="vehicle-make">Make</Label>
                    <Input
                      id="vehicle-make"
                      className="font-medium"
                      value={vehicleMake}
                      onChange={e => setVehicleMake(e.target.value)}
                      placeholder="Make"
                    />
                  </div>
                  <div className="flex-1">
                    <Label htmlFor="vehicle-model">Model</Label>
                    <Input
                      id="vehicle-model"
                      className="font-medium"
                      value={vehicleModel}
                      onChange={e => setVehicleModel(e.target.value)}
                      placeholder="Model"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-x-4 text-sm">
                  <div>
                    <Label htmlFor="vehicle-reg">Registration Number</Label>
                    <Input
                      id="vehicle-reg"
                      value={vehicleReg}
                      onChange={e => setVehicleReg(e.target.value)}
                      placeholder="Registration Number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle-color">Color</Label>
                    <Input
                      id="vehicle-color"
                      value={vehicleColor}
                      onChange={e => setVehicleColor(e.target.value)}
                      placeholder="Color"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle-engine">Engine Number</Label>
                    <Input
                      id="vehicle-engine"
                      value={vehicleEngine}
                      onChange={e => setVehicleEngine(e.target.value)}
                      placeholder="Engine Number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="vehicle-chassis">Chassis Number</Label>
                    <Input
                      id="vehicle-chassis"
                      value={vehicleChassis}
                      onChange={e => setVehicleChassis(e.target.value)}
                      placeholder="Chassis Number"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <ClipboardList className="mr-2 h-5 w-5 text-primary" />
              Job Description
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-base mb-1">Description</h3>
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2"
                  value={description}
                  onChange={(e) => setJobDescription(e.target.value)}
                />
              </div>
              <div>
                <h3 className="font-medium text-base mb-1">Notes</h3>
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2"
                  value={remarks}
                  onChange={(e) => setRemarks(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="parts" className="w-full print-hidden">
          <TabsList className="mb-4">
            <TabsTrigger value="parts">Parts</TabsTrigger>
            <TabsTrigger value="services">Services</TabsTrigger>
          </TabsList>
          <TabsContent value="parts" data-parts-content>
            <Card className="print-section">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <Package className="mr-2 h-5 w-5 text-primary" />
                  Parts
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddPartDialog(true)}
                  className="print-hidden"
                >
                  <PlusCircle className="h-4 w-4 mr-1" /> Add Part
                </Button>
              </CardHeader>
              <CardContent>
                {submittedParts && submittedParts.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50 border-b">
                          <th className="text-left p-2">Part</th>
                          <th className="text-center p-2">Price</th>
                        </tr>
                      </thead>
                      <tbody>
                        {submittedParts.map((part: any, idx: number) => (
                          <tr key={idx} className="border-b">
                            <td className="p-2">
                              <div>
                                <p className="font-medium">
                                  {part.itemName || `Part #${part.id}`}
                                </p>
                              </div>
                            </td>
                            <td className="text-center p-2">{part.price}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500 py-4 text-center">
                    No parts added to this job card yet.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="services" data-services-content>
            <Card className="print-section">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center text-lg">
                  <Wrench className="mr-2 h-5 w-5 text-primary" />
                  Services
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddServiceDialog(true)}
                  className="print-hidden"
                >
                  <PlusCircle className="h-4 w-4 mr-1" /> Add Service
                </Button>
              </CardHeader>
              <CardContent>
                {submittedServices && submittedServices.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50 border-b">
                          <th className="text-left p-2">Service</th>
                          <th className="text-center p-2">Price</th>
                        </tr>
                      </thead>
                      <tbody>
                        {submittedServices.map((service: any, idx: number) => (
                          <tr key={idx} className="border-b">
                            <td className="p-2">
                              <div>
                                <p className="font-medium">
                                  {service.itemName || `Service #${service.id}`}
                                </p>
                              </div>
                            </td>
                            <td className="text-center p-2">{service.price}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500 py-4 text-center">
                    No services added to this job card yet.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card className="mt-6 print-show">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <CalendarDays className="mr-2 h-5 w-5 text-primary" />
              Total Cost Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2 max-w-xs ml-auto">
              <div className="flex justify-between">
                <span>Parts Subtotal:</span>
                <span>
                  AED{" "}
                  {submittedParts
                    ? submittedParts
                        .reduce(
                          (total: number, part: any) =>
                            total + parseFloat(part.price),
                          0
                        )
                        .toFixed(2)
                    : "0.00"}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Services Subtotal:</span>
                <span>
                  AED{" "}
                  {submittedServices
                    ? submittedServices
                        .reduce(
                          (total: number, service: any) =>
                            total + parseFloat(service.price),
                          0
                        )
                        .toFixed(2)
                    : "0.00"}
                </span>
              </div>
              <div className="flex justify-between font-semibold pt-2 border-t">
                <span>Total:</span>
                <span>AED {calculateTotalCost().toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Signature Section */}
        {/* <div className="mt-8 print-section signature-section">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customer Approval</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <p className="text-sm">
                  I,{" "}
                  <span className="font-semibold">
                    {customerName || "____________________"}
                  </span>
                  , confirm that the above services and parts have been
                  explained to me, and I authorize MRS Automaintenance LLP to
                  proceed with the work as described.
                </p>
                <div className="grid grid-cols-2 gap-8 mt-6">
                  <div className="space-y-2">
                    <div className="border-b border-dashed border-gray-400 h-10 flex items-end signature-line">
                      <p className="text-xs text-gray-500">
                        Customer Signature
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="border-b border-dashed border-gray-400 h-10 flex items-end signature-line">
                      <p className="text-xs text-gray-500">Date</p>
                    </div>
                  </div>
                </div>
                <div className="pt-6 border-t border-gray-200 mt-6">
                  <p className="text-xs text-gray-600">
                    MRS Automaintenance LLP | Dubai, UAE | Tel: +971 4 123 4567
                    | Email: <EMAIL>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div> */}
      </div>

      {/* Add Part Dialog */}
      <Dialog open={showAddPartDialog} onOpenChange={setShowAddPartDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Parts</DialogTitle>
            <DialogDescription>
              Add one or more parts to this job card with respective price and
              notes.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2 relative">
              <Label htmlFor="part-search">Part Name</Label>
              <Input
                id="part-search"
                placeholder="Type part name"
                value={partSearch}
                onChange={handlePartSearch}
                autoComplete="off"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input
                id="price"
                type="number"
                value={partPrize}
                onChange={(e) => setPartPrize(e.target.value)}
                min={1}
              />
            </div>
            <Button
              variant="secondary"
              onClick={handleAddSinglePart}
              disabled={!partSearch || !partPrize}
            >
              + Add to List
            </Button>
            {addedParts.length > 0 && (
              <div className="mt-4 space-y-3 border-t pt-4">
                <div className="font-medium">Parts to be added:</div>
                <div className="max-h-48 overflow-y-auto space-y-3 pr-1">
                  {addedParts.map((p, idx) => (
                    <div
                      key={idx}
                      className="flex justify-between items-start border p-2 rounded"
                    >
                      <div>
                        <div className="font-semibold">{p.itemName}</div>
                        <div className="text-sm text-muted-foreground">
                          Price: AED {p.price}
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemovePart(idx)}
                        className="text-red-500 text-sm hover:underline"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAddPartDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitAllParts}
              disabled={addedParts.length === 0}
            >
              Submit All
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Service Dialog */}
      <Dialog
        open={showAddServiceDialog}
        onOpenChange={setShowAddServiceDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Services</DialogTitle>
            <DialogDescription>
              Add one or more services to this job card with respective price
              and notes.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2 relative">
              <Label htmlFor="service-search">Service Name</Label>
              <Input
                id="service-search"
                placeholder="Type service name"
                value={serviceSearch}
                onChange={handleServiceSearch}
                autoComplete="off"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="service-price">Price</Label>
              <Input
                id="service-price"
                type="number"
                value={servicePrize}
                onChange={(e) => setServicePrize(e.target.value)}
                min={1}
              />
            </div>
            <Button
              variant="secondary"
              onClick={handleAddSingleService}
              disabled={!serviceSearch || !servicePrize}
            >
              + Add to List
            </Button>
            {addedServices.length > 0 && (
              <div className="mt-4 space-y-3 border-t pt-4">
                <div className="font-medium">Services to be added:</div>
                <div className="max-h-48 overflow-y-auto space-y-3 pr-1">
                  {addedServices.map((s, idx) => (
                    <div
                      key={idx}
                      className="flex justify-between items-start border p-2 rounded"
                    >
                      <div>
                        <div className="font-semibold">{s.itemName}</div>
                        <div className="text-sm text-muted-foreground">
                          Price: AED {s.price}
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemoveService(idx)}
                        className="text-red-500 text-sm hover:underline"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAddServiceDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitAllServices}
              disabled={addedServices.length === 0}
            >
              Submit All
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Invoice Preview Dialog */}
      <Dialog open={showInvoicePreview} onOpenChange={setShowInvoicePreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle> Quotation Preview </DialogTitle>
            <DialogDescription>
              Please preview and send quotation to customer via email or print.
            </DialogDescription>
          </DialogHeader>
          <div className="border rounded-md p-4 bg-gray-50 mt-4">
            <h3 className="font-semibold mb-2">Contact Information</h3>
            <p className="text-sm text-gray-600 mb-2">
              Add contact details for sending the invoice:
            </p>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-blue-500" />
                <Label htmlFor="email-contact">Email</Label>
                <Input
                  id="email-contact"
                  value={emailContact}
                  onChange={(e) => setEmailContact(e.target.value)}
                  placeholder="<EMAIL>"
                  className="flex-1 ml-2"
                />
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between mt-6 w-full">
            <div className="flex space-x-2 w-full justify-end">
              <Button
                type="button"
                variant="outline"
                className="flex items-center gap-1 bg-blue-50 text-blue-700 hover:bg-blue-100"
                onClick={() => sendInvoiceEmail(emailContact)}
              >
                <Mail className="h-4 w-4" /> Send via Email
              </Button>
              <Button
                type="button"
                variant="outline"
                className="flex items-center gap-1 bg-green-50 text-green-700 hover:bg-green-100"
                onClick={printJobCard}
              >
                <FileDown className="h-4 w-4" /> Download Quotation
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CreateJobCard;
